import React, { useState, useEffect, useRef } from 'react';
import { YTHForm, YTHList } from 'yth-ui';
import { message, Button, Spin, Card, Row, Col, Statistic, Tag, Divider } from 'antd';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import moment from 'moment';
import surroundingEnvApi from '@/service/surroundingEnvApi';
import type { ApiResponse, SurroundingEnvRecord, EnvironmentDetail } from '@/service/surroundingEnvApi';
import type { Form } from '@formily/core/esm/models';

type PropsTypes = {
  dataObj: SurroundingEnvRecord;
  closeModal: () => void;
};

/**
 * @description 园区周边环境详情弹窗
 * @returns
 */
const SurroundingEnvModal: React.FC<PropsTypes> = ({ dataObj, closeModal = () => {} }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [environmentDetail, setEnvironmentDetail] = useState<EnvironmentDetail>();
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  // YTHList 相关状态
  const listActionRef: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();

  // 历史数据列表列配置
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'monitorTime',
      title: '监测时间',
      width: 160,
      display: true,
      render: (value: string) => {
        return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      dataIndex: 'monitorValue',
      title: '监测值',
      width: 120,
      display: true,
      render: (value: number, record: SurroundingEnvRecord) => {
        return `${value || 0} ${record.measureUnit || ''}`;
      },
    },
    {
      dataIndex: 'standardValue',
      title: '标准值',
      width: 120,
      display: true,
      render: (value: number, record: SurroundingEnvRecord) => {
        return `${value || 0} ${record.measureUnit || ''}`;
      },
    },
    {
      dataIndex: 'environmentStatus',
      title: '环境状态',
      width: 120,
      display: true,
      render: (value: string, record: SurroundingEnvRecord) => {
        const getStatusColor = (status: string): string => {
          switch (status) {
            case 'A22A09A01': return 'green';
            case 'A22A09A02': return 'orange';
            case 'A22A09A03': return 'red';
            case 'A22A09A04': return 'purple';
            default: return 'default';
          }
        };
        return (
          <Tag color={getStatusColor(value)}>
            {record.environmentStatusText || '-'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'riskLevel',
      title: '风险等级',
      width: 100,
      display: true,
      render: (value: string, record: SurroundingEnvRecord) => {
        const getRiskLevelColor = (riskLevel: string): string => {
          switch (riskLevel) {
            case 'A22A10A01': return 'green';
            case 'A22A10A02': return 'orange';
            case 'A22A10A03': return 'red';
            default: return 'default';
          }
        };
        return (
          <Tag color={getRiskLevelColor(value)}>
            {record.riskLevelText || '-'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'remarks',
      title: '备注',
      width: 200,
      display: true,
      render: (value: string) => {
        return value || '-';
      },
    },
  ];

  useEffect(() => {
    if (dataObj && dataObj.id && dataObj.id !== '') {
      // 设置基本信息
      form.setValues({
        ...dataObj,
        id: dataObj.id,
        viewTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      });

      // 获取详细信息
      setIsLoading(true);
      surroundingEnvApi.querySurroundingEnvDetail({ id: dataObj.id })
        .then((res: ApiResponse<EnvironmentDetail>) => {
          if (res.code === 200) {
            setEnvironmentDetail(res.data);
          } else {
            message.error(res.msg || '获取详情失败');
          }
        })
        .catch((error) => {
          message.error('获取详情失败');
          console.error('获取环境详情失败:', error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      message.error('数据出错').then(() => {
        closeModal();
      });
    }
  }, [dataObj, form, closeModal]);

  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  return (
    <div>
      <Spin spinning={isLoading}>
        {/* 基本信息表单 */}
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="environmentTypeText"
            title="环境类型"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="monitorAreaText"
            title="监测区域"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="monitorIndexText"
            title="监测指标"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="equipmentName"
            title="监测设备"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="monitorTime"
            title="监测时间"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: true,
              value: dataObj?.monitorTime ? moment(dataObj.monitorTime).format('YYYY-MM-DD HH:mm:ss') : '-',
            }}
          />
          <YTHForm.Item
            name="viewTime"
            title="查看时间"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
        </YTHForm>

        {/* 环境详情卡片 */}
        {environmentDetail && (
          <>
            <Divider orientation="left">环境监测详情</Divider>
            <Row gutter={16} style={{ marginBottom: '20px' }}>
              <Col span={8}>
                <Card title="设备信息" size="small">
                  <Statistic 
                    title="设备编号" 
                    value={environmentDetail.equipmentInfo.equipmentCode || '-'} 
                    valueStyle={{ fontSize: '14px' }}
                  />
                  <Statistic 
                    title="设备类型" 
                    value={environmentDetail.equipmentInfo.equipmentType || '-'} 
                    valueStyle={{ fontSize: '14px' }}
                  />
                  <Statistic 
                    title="安装位置" 
                    value={environmentDetail.equipmentInfo.installLocation || '-'} 
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title="监测数据" size="small">
                  <Statistic 
                    title="当前值" 
                    value={environmentDetail.monitorData.currentValue || 0} 
                    suffix={dataObj?.measureUnit || ''}
                    valueStyle={{ fontSize: '14px' }}
                  />
                  <Statistic 
                    title="超标倍数" 
                    value={environmentDetail.monitorData.exceedMultiple || 0} 
                    suffix="倍"
                    valueStyle={{ 
                      fontSize: '14px',
                      color: environmentDetail.monitorData.exceedMultiple > 1 ? '#ff4d4f' : '#52c41a'
                    }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title="环境评估" size="small">
                  <Statistic 
                    title="质量等级" 
                    value={environmentDetail.environmentAssessment.qualityLevel || '-'} 
                    valueStyle={{ fontSize: '14px' }}
                  />
                  <Statistic 
                    title="污染程度" 
                    value={environmentDetail.environmentAssessment.pollutionLevel || '-'} 
                    valueStyle={{ fontSize: '14px' }}
                  />
                  <Statistic 
                    title="影响范围" 
                    value={environmentDetail.environmentAssessment.impactScope || '-'} 
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Card>
              </Col>
            </Row>

            {/* 建议措施 */}
            {environmentDetail.environmentAssessment.suggestedMeasures && (
              <Card title="建议措施" size="small" style={{ marginBottom: '20px' }}>
                <p>{environmentDetail.environmentAssessment.suggestedMeasures}</p>
              </Card>
            )}
          </>
        )}

        {/* 历史数据列表 */}
        <div style={{ marginTop: '20px', display: dataObj?.equipmentId ? 'block' : 'none' }}>
          <span style={{ marginLeft: '10px', fontSize: '14px' }}>历史监测数据</span>
          <YTHList
            defaultQuery={{}}
            code="environmentHistoryDataList"
            action={listAction}
            actionRef={listActionRef}
            showRowSelection={false}
            operation={[]}
            listKey="id"
            extraOperation={[]}
            request={async (filter, pagination) => {
              try {
                const resData: ApiResponse<SurroundingEnvRecord[]> = await surroundingEnvApi.queryEnvironmentHistoryData({
                  aescs: [],
                  descs: [],
                  condition: { 
                    equipmentId: dataObj?.equipmentId,
                    startTime: moment().subtract(7, 'days').format('YYYY-MM-DD'),
                    endTime: moment().format('YYYY-MM-DD')
                  },
                  currentPage: pagination.current,
                  pageSize: pagination.pageSize,
                });

                if (resData.code === 200) {
                  resData.data.forEach((item, index) => {
                    resData.data[index].serialNo =
                      (pagination.current - 1) * pagination.pageSize + index + 1;
                  });
                  return {
                    data: resData.data,
                    total: resData.total,
                    success: true,
                  };
                }
                return {
                  data: [],
                  total: 0,
                  success: false,
                };
              } catch (error) {
                console.error('获取历史数据失败:', error);
                return {
                  data: [],
                  total: 0,
                  success: false,
                };
              }
            }}
            columns={columns}
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
            }}
          />
        </div>

        {/* 底部按钮 */}
        <div style={{ textAlign: 'right', marginTop: '20px' }}>
          <Button onClick={cancel}>关闭</Button>
        </div>
      </Spin>
    </div>
  );
};

export default SurroundingEnvModal;
