import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, Tag } from 'antd';
import surroundingEnvApi from '@/service/surroundingEnvApi';
import type {
  ApiResponse,
  SurroundingEnvRecord,
  SurroundingEnvQueryParams,
} from '@/service/surroundingEnvApi';
import locales from '@/locales';
import formApi from '@/service/formApi';
import moment from 'moment';
import SurroundingEnvModal from './components/SurroundingEnvModal';

/**
 * @description 园区周边环境管理页面
 * @returns
 */
const SurroundingEnvironmentList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [dataObj, setDataObj] = useState<SurroundingEnvRecord>();
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  /**
   * 关闭弹窗
   */
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    setDataObj(undefined);
  };

  /**
   * 处理筛选条件
   */
  const handleFilter: (filter: {
    areaName: string;
    category: { code: string; text: string }[];
  }) => SurroundingEnvQueryParams = (filter) => {
    const condition: SurroundingEnvQueryParams = {};
    condition.areaName = filter.areaName;
    condition.category = filter.category?.[0]?.code;
    return condition;
  };

  /**
   * 表格列配置
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'areaName',
      title: '敏感区域名称',
      width: 150,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      dataIndex: 'category',
      title: '类别',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A22A08A08', // 环境类型字典
            },
            currentPage: 0,
            pageSize: 0,
          });
          return list;
        },
        p_props: {
          placeholder: '请选择环境类型',
        },
      },
      render: (value: string, record: SurroundingEnvRecord) => {
        return record.categoryText || '-';
      },
    },
    {
      dataIndex: 'updateBy',
      title: '更新人',
      width: 150,
      query: false,
      display: true,
    },
  ];

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="surroundingEnvironmentList"
        action={aa}
        searchMemory
        actionRef={ref}
        showRowSelection={false}
        operation={[]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ApiResponse<SurroundingEnvRecord[]> =
            await surroundingEnvApi.querySurroundingEnvList({
              aescs: [],
              descs: [],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            });
          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={70}
        rowOperation={(row: SurroundingEnvRecord) => {
          return [
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setDataObj(row);
                    setEditMenuVisiable(true);
                  }}
                >
                  查看
                </Button>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        title="园区周边环境详情"
        width="80%"
        footer={null}
        destroyOnClose
        onCancel={closeModal}
        maskClosable={false}
        visible={editMenuVisiable}
        key="surrounding-environment-modal"
      >
        <SurroundingEnvModal closeModal={closeModal} dataObj={dataObj} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(
  SurroundingEnvironmentList,
  locales,
  YTHLocalization.getLanguage(),
);
